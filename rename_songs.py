#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to rename YouTube downloaded music files by removing the prefix pattern.
Removes patterns like "[ youtube.com ] 248+251 - " from filenames.
"""

import os
import re
import sys

def rename_youtube_files(directory="."):
    """
    Rename files in the given directory by removing YouTube prefix patterns.
    
    Args:
        directory (str): Directory path to process (default: current directory)
    """
    # Pattern to match: [ youtube.com ] followed by numbers+numbers - 
    # This will match patterns like "[ youtube.com ] 248+251 - " or "[ youtube.com ] 18 - "
    pattern = r'^\[ youtube\.com \] \d+(\+\d+)? - '
    
    # Get all files in the directory
    files = [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
    
    # Filter files that match the YouTube pattern
    youtube_files = [f for f in files if re.match(pattern, f)]
    
    if not youtube_files:
        print("No YouTube files found with the expected pattern.")
        return
    
    print(f"Found {len(youtube_files)} files to rename:")
    print("-" * 50)
    
    renamed_count = 0
    
    for filename in youtube_files:
        # Remove the YouTube prefix
        new_name = re.sub(pattern, '', filename)
        
        old_path = os.path.join(directory, filename)
        new_path = os.path.join(directory, new_name)
        
        # Check if the new filename already exists
        if os.path.exists(new_path):
            print(f"⚠️  SKIP: '{new_name}' already exists")
            continue
        
        try:
            os.rename(old_path, new_path)
            print(f"✅ '{filename}' → '{new_name}'")
            renamed_count += 1
        except OSError as e:
            print(f"❌ ERROR renaming '{filename}': {e}")
    
    print("-" * 50)
    print(f"Successfully renamed {renamed_count} out of {len(youtube_files)} files.")

def preview_changes(directory="."):
    """
    Preview what changes would be made without actually renaming files.
    
    Args:
        directory (str): Directory path to process (default: current directory)
    """
    pattern = r'^\[ youtube\.com \] \d+(\+\d+)? - '
    
    files = [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
    youtube_files = [f for f in files if re.match(pattern, f)]
    
    if not youtube_files:
        print("No YouTube files found with the expected pattern.")
        return
    
    print(f"Preview: {len(youtube_files)} files would be renamed:")
    print("-" * 70)
    
    for filename in youtube_files:
        new_name = re.sub(pattern, '', filename)
        new_path = os.path.join(directory, new_name)
        
        status = "⚠️  (would skip - file exists)" if os.path.exists(new_path) else "✅"
        print(f"{status} '{filename}'")
        print(f"    → '{new_name}'")
        print()

def main():
    """Main function to handle command line arguments and execute the script."""
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage:")
            print("  python rename_songs.py          # Rename files in current directory")
            print("  python rename_songs.py preview  # Preview changes without renaming")
            print("  python rename_songs.py -h       # Show this help")
            return
        elif sys.argv[1] == 'preview':
            preview_changes()
            return
    
    # Ask for confirmation before proceeding
    print("This script will rename YouTube downloaded files by removing the prefix.")
    print("Example: '[ youtube.com ] 248+251 - blue.mp3' → 'blue.mp3'")
    print()
    
    # Show preview first
    preview_changes()
    
    response = input("\nDo you want to proceed with renaming? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\nProceeding with renaming...")
        rename_youtube_files()
    else:
        print("Operation cancelled.")

if __name__ == "__main__":
    main()
